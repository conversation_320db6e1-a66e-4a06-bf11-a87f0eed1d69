import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'

export default function Login(){
  const { login } = useAuth()
  const nav = useNavigate()
  const [email, setEmail] = useState('')
  const [pwd, setPwd] = useState('')
  const [err, setErr] = useState('')
  const [loading, setLoading] = useState(false)
  const [show, setShow] = useState(false)

  async function onSubmit(e){
    e.preventDefault()
    setErr('')
    if (!email || !pwd) return setErr('Enter email and password')
    setLoading(true)
    try {
      await login(email.trim().toLowerCase(), pwd)
      nav('/dashboard')
    } catch (e) {
      setErr(e.message)
    } finally { setLoading(false) }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        <div className="text-center mb-6">
          <div className="text-sud-gold text-4xl font-semibold">Su<PERSON><PERSON>han</div>
          <p className="text-sm text-gray-300 mt-2">Sign in to continue</p>
        </div>

        <form className="bg-sud-navy/40 p-6 rounded-xl" onSubmit={onSubmit}>
          {err && <div className="text-red-400 mb-3">{err}</div>}
          <label className="block mb-3">
            <span className="text-sm text-gray-200">Email</span>
            <input value={email} onChange={e=>setEmail(e.target.value)} className="mt-1 block w-full p-3 rounded-lg bg-sud-navy/80 border border-sud-gold/10" placeholder="<EMAIL>"/>
          </label>

          <label className="block mb-4">
            <span className="text-sm text-gray-200">Password</span>
            <div className="relative">
              <input type={show ? 'text' : 'password'} value={pwd} onChange={e=>setPwd(e.target.value)} className="mt-1 block w-full p-3 rounded-lg bg-sud-navy/80 border border-sud-gold/10" placeholder="Password"/>
              <button type="button" onClick={()=>setShow(s=>!s)} className="absolute right-3 top-3 text-sm text-gray-300">{show? 'Hide':'Show'}</button>
            </div>
          </label>

          <button disabled={loading} className="w-full btn-gold mb-3">{loading ? 'Signing in...' : 'Sign in'}</button>
          <div className="text-center text-sm text-gray-300">New? <Link to="/register" className="text-sud-gold">Create account</Link></div>
        </form>
      </div>
    </div>
  )
}
