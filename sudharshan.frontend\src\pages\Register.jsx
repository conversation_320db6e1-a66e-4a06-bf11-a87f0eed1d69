import React, { useState } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'

export default function Register(){
  const { register } = useAuth()
  const nav = useNavigate()
  const [email, setEmail] = useState('')
  const [pwd, setPwd] = useState('')
  const [confirm, setConfirm] = useState('')
  const [err, setErr] = useState('')
  const [loading, setLoading] = useState(false)
  const [show, setShow] = useState(false)

  async function onSubmit(e){
    e.preventDefault()
    setErr('')
    if (!email || !pwd) return setErr('Fill all fields')
    if (pwd.length < 6) return setErr('Password must be >= 6 chars')
    if (pwd !== confirm) return setErr('Passwords do not match')
    setLoading(true)
    try {
      await register(email.trim().toLowerCase(), pwd)
      nav('/dashboard')
    } catch (e) {
      setErr(e.message)
    } finally { setLoading(false) }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        <div className="text-center mb-6">
          <div className="text-sud-gold text-4xl font-semibold">Sudharshan</div>
          <p className="text-sm text-gray-300 mt-2">Create your account</p>
        </div>

        <form className="bg-sud-navy/40 p-6 rounded-xl" onSubmit={onSubmit}>
          {err && <div className="text-red-400 mb-3">{err}</div>}
          <label className="block mb-3">
            <span className="text-sm text-gray-200">Email</span>
            <input value={email} onChange={e=>setEmail(e.target.value)} className="mt-1 block w-full p-3 rounded-lg bg-sud-navy/80 border border-sud-gold/10" placeholder="<EMAIL>"/>
          </label>

          <label className="block mb-3">
            <span className="text-sm text-gray-200">Password</span>
            <div className="relative">
              <input type={show ? 'text':'password'} value={pwd} onChange={e=>setPwd(e.target.value)} className="mt-1 block w-full p-3 rounded-lg bg-sud-navy/80 border border-sud-gold/10" placeholder="Enter password"/>
              <button type="button" onClick={()=>setShow(s=>!s)} className="absolute right-3 top-3 text-sm text-gray-300">{show? 'Hide':'Show'}</button>
            </div>
          </label>

          <label className="block mb-4">
            <span className="text-sm text-gray-200">Confirm Password</span>
            <input value={confirm} onChange={e=>setConfirm(e.target.value)} className="mt-1 block w-full p-3 rounded-lg bg-sud-navy/80 border border-sud-gold/10" placeholder="Confirm password"/>
          </label>

          <button disabled={loading} className="w-full btn-gold mb-3">{loading ? 'Creating...' : 'Create account'}</button>
          <div className="text-center text-sm text-gray-300">Already have an account? <Link to="/login" className="text-sud-gold">Sign in</Link></div>
        </form>
      </div>
    </div>
  )
}
