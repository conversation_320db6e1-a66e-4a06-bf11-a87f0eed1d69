import React from 'react'
import { useAuth } from '../hooks/useAuth'

export default function Dashboard(){
  const { user, logout } = useAuth()
  return (
    <div className="min-h-screen p-6">
      <header className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full flex items-center justify-center bg-sud-gold/10 text-sud-gold border border-sud-gold/20">
            {/* small chakra */}
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" className="opacity-90">
              <circle cx="12" cy="12" r="10" stroke="#D4AF37"/>
              <path d="M12 4v16M4 12h16" stroke="#D4AF37" strokeWidth="1.2"/>
            </svg>
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-sud-gold"><PERSON><PERSON><PERSON><PERSON></h1>
            <div className="text-sm text-gray-300">Welcome, {user?.email}</div>
          </div>
        </div>
        <div>
          <button onClick={logout} className="btn-gold">Logout</button>
        </div>
      </header>

      <main className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <h3 className="text-lg text-sud-gold mb-2">Professionalism</h3>
          <div className="text-4xl font-bold">9.73</div>
          <div className="text-sm text-gray-300 mt-2">Required: 9.8</div>
        </div>

        <div className="card">
          <h3 className="text-lg text-sud-gold mb-2">Attendance</h3>
          <div className="text-4xl font-bold">91%</div>
          <div className="text-sm text-gray-300 mt-2">Period: Aug 8 — Sep 30</div>
        </div>

        <div className="card">
          <h3 className="text-lg text-sud-gold mb-2">Coding</h3>
          <div className="flex gap-2">
            <div className="w-6 h-6 bg-yellow-400 rounded-sm"></div>
            <div className="w-6 h-6 bg-green-500 rounded-sm"></div>
            <div className="w-6 h-6 bg-violet-600 rounded-sm"></div>
            <div className="w-6 h-6 bg-blue-500 rounded-sm"></div>
            <div className="w-6 h-6 bg-gray-400 rounded-sm"></div>
          </div>
          <div className="mt-4">
            <button className="btn-gold">Add Belt</button>
          </div>
        </div>
      </main>
    </div>
  )
}
