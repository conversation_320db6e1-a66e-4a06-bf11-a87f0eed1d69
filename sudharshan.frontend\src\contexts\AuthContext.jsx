import React, { useState, useEffect } from 'react'
import { registerUser, loginUser, logout as doLogout, getSession } from '../utils/auth'
import { AuthContext } from './AuthContextDefinition'

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const s = getSession()
    if (s && s.email) setUser({ email: s.email })
    setLoading(false)
  }, [])

  const register = async (email, pwd) => {
    await registerUser(email, pwd)
    // auto-login after register
    const session = await loginUser(email, pwd)
    setUser({ email: session.email })
  }

  const login = async (email, pwd) => {
    const session = await loginUser(email, pwd)
    setUser({ email: session.email })
  }

  const logout = () => {
    doLogout()
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, loading, register, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}
